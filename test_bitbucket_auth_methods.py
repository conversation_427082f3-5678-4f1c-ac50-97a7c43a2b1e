#!/usr/bin/env python3
"""
Test script for modern Bitbucket authentication methods
"""
import requests
import getpass
import json

def test_token_auth():
    """Test using access token authentication"""
    repo = "cf7skins/cf7skins-single-cf7skins-single"
    
    print("=== Bitbucket Access Token Authentication Test ===")
    print(f"Repository: {repo}")
    print()
    
    print("Choose authentication method:")
    print("1. Workspace Access Token (recommended)")
    print("2. Repository Access Token")
    print("3. Personal Access Token")
    print("4. Username + Password (legacy)")
    
    choice = input("\nEnter choice (1-4): ").strip()
    
    if choice in ["1", "2", "3"]:
        token = getpass.getpass("Enter your Access Token: ")
        headers = {
            "Authorization": f"Bearer {token}",
            "Accept": "application/json"
        }
        auth_method = "Token"
        auth = None
    else:
        username = input("Enter username: ").strip() or "buzztone"
        password = getpass.getpass("Enter password/app password: ")
        headers = {"Accept": "application/json"}
        auth = (username, password)
        auth_method = "Basic Auth"
    
    # Test endpoints
    endpoints = [
        ("Repository Info", f"https://api.bitbucket.org/2.0/repositories/{repo}"),
        ("Issues List", f"https://api.bitbucket.org/2.0/repositories/{repo}/issues"),
        ("User Info", "https://api.bitbucket.org/2.0/user"),
        ("Workspaces", "https://api.bitbucket.org/2.0/workspaces"),
    ]
    
    print(f"\nTesting with {auth_method}...")
    print("-" * 60)
    
    for name, url in endpoints:
        try:
            if auth:
                response = requests.get(url, auth=auth, headers=headers)
            else:
                response = requests.get(url, headers=headers)
                
            status = response.status_code
            
            print(f"\n{name}:")
            print(f"  Status: {status}", end="")
            
            if status == 200:
                print(" ✅ SUCCESS")
                try:
                    data = response.json()
                    if 'full_name' in data:
                        print(f"  Repository: {data['full_name']}")
                        print(f"  Private: {data.get('is_private', 'Unknown')}")
                        print(f"  Has Issues: {data.get('has_issues', 'Unknown')}")
                    elif 'username' in data:
                        print(f"  User: {data['username']}")
                    elif 'size' in data:
                        print(f"  Issues found: {data['size']}")
                    elif 'values' in data and isinstance(data['values'], list):
                        print(f"  Items found: {len(data['values'])}")
                except:
                    print("  (Response data available)")
                    
            elif status == 401:
                print(" ❌ UNAUTHORIZED")
                print("  → Check your token/credentials")
            elif status == 403:
                print(" ❌ FORBIDDEN")
                print("  → Insufficient permissions")
            elif status == 404:
                print(" ❌ NOT FOUND")
                print("  → Check repository name or access")
            else:
                print(f" ⚠️  STATUS {status}")
                
        except Exception as e:
            print(f"  ❌ ERROR: {str(e)}")
    
    print("\n" + "-" * 60)
    return choice, token if choice in ["1", "2", "3"] else None, auth

def test_migration_endpoints():
    """Test specific endpoints needed for migration"""
    choice, token, auth = test_token_auth()
    
    if choice not in ["1", "2", "3", "4"]:
        return
        
    repo = "cf7skins/cf7skins-single-cf7skins-single"
    
    print("\n=== Testing Migration-Specific Endpoints ===")
    
    # Headers setup
    if token:
        headers = {
            "Authorization": f"Bearer {token}",
            "Accept": "application/json"
        }
        auth_params = {"headers": headers}
    else:
        headers = {"Accept": "application/json"}
        auth_params = {"auth": auth, "headers": headers}
    
    # Migration-specific endpoints
    migration_endpoints = [
        ("Issues with pagination", f"https://api.bitbucket.org/2.0/repositories/{repo}/issues?pagelen=1"),
        ("Issue comments", f"https://api.bitbucket.org/2.0/repositories/{repo}/issues/1/comments"),
        ("Issue changes", f"https://api.bitbucket.org/2.0/repositories/{repo}/issues/1/changes"),
        ("Attachments", f"https://api.bitbucket.org/2.0/repositories/{repo}/issues/1/attachments"),
    ]
    
    for name, url in migration_endpoints:
        try:
            response = requests.get(url, **auth_params)
            status = response.status_code
            
            print(f"\n{name}:")
            print(f"  URL: {url}")
            print(f"  Status: {status}", end="")
            
            if status == 200:
                print(" ✅ SUCCESS")
            elif status == 404:
                print(" ⚠️  NOT FOUND (may be normal if no issues exist)")
            elif status == 403:
                print(" ❌ FORBIDDEN")
            elif status == 401:
                print(" ❌ UNAUTHORIZED")
            else:
                print(f" ⚠️  STATUS {status}")
                
        except Exception as e:
            print(f"  ❌ ERROR: {str(e)}")

if __name__ == "__main__":
    test_migration_endpoints()
