[project]
name = "bitbucket-migrator"
version = "0.1.0"
description = ""
authors = [
    {name = "<PERSON>",email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "requests (>=2.32.5,<3.0.0)"
]

[tool.poetry]
packages = [{include = "bitbucket_migrator", from = "src"}]


[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"
