#!/usr/bin/env python3
"""
Test script to verify Bitbucket repository access
"""
import requests
import getpass

def test_bitbucket_access():
    # Repository details
    username = "buzztone"
    repo = "cf7skins/cf7skins-single-cf7skins-single"
    
    # Get credentials
    print(f"Testing access for user: {username}")
    print(f"Repository: {repo}")
    print()
    
    password = getpass.getpass("Enter your Bitbucket App Password: ")
    auth = (username, password)
    
    # Test endpoints
    endpoints = [
        ("Repository Info", f"https://api.bitbucket.org/2.0/repositories/{repo}"),
        ("Issues Endpoint", f"https://api.bitbucket.org/2.0/repositories/{repo}/issues"),
        ("User Info", f"https://api.bitbucket.org/2.0/user"),
    ]
    
    print("Testing API endpoints...")
    print("-" * 50)
    
    for name, url in endpoints:
        try:
            response = requests.head(url, auth=auth)
            status = response.status_code
            
            if status == 200:
                print(f"✅ {name}: SUCCESS (200)")
            elif status == 403:
                print(f"❌ {name}: PERMISSION DENIED (403)")
                print(f"   URL: {url}")
            elif status == 404:
                print(f"❌ {name}: NOT FOUND (404)")
                print(f"   URL: {url}")
            elif status == 401:
                print(f"❌ {name}: AUTHENTICATION FAILED (401)")
            else:
                print(f"⚠️  {name}: UNEXPECTED STATUS ({status})")
                
        except Exception as e:
            print(f"❌ {name}: ERROR - {str(e)}")
    
    print("-" * 50)
    
    # Additional repository info test
    print("\nTesting repository details...")
    try:
        response = requests.get(f"https://api.bitbucket.org/2.0/repositories/{repo}", auth=auth)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Repository found: {data.get('full_name')}")
            print(f"   Private: {data.get('is_private', 'Unknown')}")
            print(f"   Has Issues: {data.get('has_issues', 'Unknown')}")
            print(f"   Owner: {data.get('owner', {}).get('display_name', 'Unknown')}")
        elif response.status_code == 403:
            print("❌ Cannot access repository details (403 Forbidden)")
            print("   This suggests you don't have read access to the repository")
        elif response.status_code == 404:
            print("❌ Repository not found (404)")
            print("   Check if the repository name is correct")
        else:
            print(f"⚠️  Unexpected response: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error testing repository: {str(e)}")

if __name__ == "__main__":
    test_bitbucket_access()
