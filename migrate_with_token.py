#!/usr/bin/env python3
"""
Modified version of migrate.py with token authentication support
Usage: python migrate_with_token.py --token YOUR_TOKEN -n repo/name target/repo username
"""

import os
import sys

# Set environment variable for token authentication
def main():
    if len(sys.argv) < 2:
        print("Usage examples:")
        print("With token:")
        print("  python migrate_with_token.py --token YOUR_ACCESS_TOKEN -n cf7skins/cf7skins-single-cf7skins-single buzztone/cf7skins buzztone")
        print()
        print("With username/password:")
        print("  python migrate_with_token.py -bu buzztone -n cf7skins/cf7skins-single-cf7skins-single buzztone/cf7skins buzztone")
        return

    # Check if token is provided
    if "--token" in sys.argv:
        token_index = sys.argv.index("--token")
        if token_index + 1 < len(sys.argv):
            token = sys.argv[token_index + 1]
            # Remove token arguments
            sys.argv.pop(token_index)  # Remove --token
            sys.argv.pop(token_index)  # Remove token value
            
            # Set environment variable that we'll check in the original script
            os.environ["BITBUCKET_TOKEN"] = token
            print(f"Using Bitbucket token authentication")
        else:
            print("Error: --token requires a value")
            return

    # Import and run the original migrate script
    try:
        import migrate
        migrate.main(migrate.read_arguments())
    except Exception as e:
        print(f"Error: {e}")
        print("\nIf you're getting permission errors, try:")
        print("1. Creating a Workspace Access Token at:")
        print("   https://bitbucket.org/cf7skins/workspace/settings/access-tokens")
        print("2. Ensure token has 'Repositories: Read' and 'Issues: Read' permissions")
        print("3. Verify you have access to the repository")

if __name__ == "__main__":
    main()
