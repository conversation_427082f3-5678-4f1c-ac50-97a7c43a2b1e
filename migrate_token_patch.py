#!/usr/bin/env python3
"""
Patch for migrate.py to support Bitbucket token authentication
This shows the changes needed to support the new authentication method
"""

# Function signature changes needed:

def get_issues(bb_url, offset, bb_auth, bb_headers=None):
    """Fetch the issues from Bitbucket."""
    next_url = bb_url
    params = {"sort": "id"}
    if offset:
        params['q'] = "id > {}".format(offset)

    while next_url is not None:
        if bb_headers and "Authorization" in bb_headers:
            # Use token authentication
            respo = requests.get(next_url, params=params, headers=bb_headers)
        else:
            # Use basic authentication
            respo = requests.get(next_url, auth=bb_auth, params=params)
            
        # Rest of function remains the same...

def get_issue_comments(issue_id, bb_url, bb_auth, bb_headers=None):
    """Fetch the comments for the specified Bitbucket issue."""
    next_url = "{bb_url}/{issue_id}/comments/".format(**locals())
    comments = []

    while next_url is not None:
        if bb_headers and "Authorization" in bb_headers:
            respo = requests.get(next_url, params={"sort": "id"}, headers=bb_headers)
        else:
            respo = requests.get(next_url, auth=bb_auth, params={"sort": "id"})
        # Rest of function remains the same...

def get_issue_changes(issue_id, bb_url, bb_auth, bb_headers=None):
    """Fetch the changes for the specified Bitbucket issue."""
    next_url = "{bb_url}/{issue_id}/changes/".format(**locals())
    changes = []

    while next_url is not None:
        if bb_headers and "Authorization" in bb_headers:
            respo = requests.get(next_url, params={"sort": "id"}, headers=bb_headers)
        else:
            respo = requests.get(next_url, auth=bb_auth, params={"sort": "id"})
        # Rest of function remains the same...

def get_attachment_names(issue_num, bb_url, bb_auth, bb_headers=None):
    """Get the names of attachments on this issue."""
    attachment_url = "{}/{}/attachments".format(bb_url.replace("1", "2"), issue_num)
    
    if bb_headers and "Authorization" in bb_headers:
        respo = requests.get(attachment_url, headers=bb_headers)
    else:
        respo = requests.get(attachment_url, auth=bb_auth)
    # Rest of function remains the same...

# Main function call updates needed:
def main(options):
    # ... existing code ...
    
    print("getting issues from bitbucket")
    issues_iterator = get_issues(bb_url, options.skip, options.bb_auth, options.bb_headers)
    
    issues_iterator = fill_gaps(issues_iterator, options.skip)
    
    for index, issue in enumerate(issues_iterator):
        if isinstance(issue, DummyIssue):
            comments = []
            changes = []
        else:
            comments = get_issue_comments(issue['id'], bb_url, options.bb_auth, options.bb_headers)
            changes = get_issue_changes(issue['id'], bb_url, options.bb_auth, options.bb_headers)

        if options.mention_attachments:
            attach_names = get_attachment_names(issue['id'], bb_url, options.bb_auth, options.bb_headers)
        else:
            attach_names = []
        # ... rest of main function ...

print("This file shows the pattern for updating the migration script.")
print("The key changes are:")
print("1. Add bb_headers parameter to all Bitbucket API functions")
print("2. Check for Authorization header to determine auth method")
print("3. Use headers for token auth, auth parameter for basic auth")
print("4. Update all function calls to pass bb_headers")
