@echo off
echo Testing Bitbucket API access with curl...
echo.

set /p USERNAME="Enter Bitbucket username (buzztone): "
if "%USERNAME%"=="" set USERNAME=buzztone

set /p PASSWORD="Enter App Password: "

echo.
echo Testing repository access...
echo ================================

echo.
echo 1. Testing repository info:
curl -u %USERNAME%:%PASSWORD% -I "https://api.bitbucket.org/2.0/repositories/cf7skins/cf7skins-single-cf7skins-single"

echo.
echo 2. Testing issues endpoint:
curl -u %USERNAME%:%PASSWORD% -I "https://api.bitbucket.org/2.0/repositories/cf7skins/cf7skins-single-cf7skins-single/issues"

echo.
echo 3. Testing user info:
curl -u %USERNAME%:%PASSWORD% -I "https://api.bitbucket.org/2.0/user"

echo.
echo ================================
echo Test complete. Check the HTTP status codes above.
echo 200 = Success, 403 = Forbidden, 404 = Not Found, 401 = Unauthorized
pause
