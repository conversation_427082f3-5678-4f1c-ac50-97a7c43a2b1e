#!/usr/bin/env python3
"""
Detailed permission test for Bitbucket API access
"""
import requests
import getpass
import json

def test_detailed_permissions():
    username = "buzztone"
    repo = "cf7skins/cf7skins-single-cf7skins-single"
    
    print(f"Detailed permission test for user: {username}")
    print(f"Repository: {repo}")
    print()
    
    password = getpass.getpass("Enter your NEW Bitbucket App Password: ")
    auth = (username, password)
    
    # Detailed test endpoints
    tests = [
        ("Account Info", "GET", "https://api.bitbucket.org/2.0/user", "Account: Read"),
        ("Repository Info", "GET", f"https://api.bitbucket.org/2.0/repositories/{repo}", "Repositories: Read"),
        ("Issues List", "GET", f"https://api.bitbucket.org/2.0/repositories/{repo}/issues", "Issues: Read"),
        ("Issues Count", "GET", f"https://api.bitbucket.org/2.0/repositories/{repo}/issues?pagelen=1", "Issues: Read"),
    ]
    
    print("Testing detailed permissions...")
    print("=" * 70)
    
    for name, method, url, required_perm in tests:
        try:
            if method == "GET":
                response = requests.get(url, auth=auth)
            else:
                response = requests.head(url, auth=auth)
                
            status = response.status_code
            
            print(f"\n{name}:")
            print(f"  URL: {url}")
            print(f"  Required Permission: {required_perm}")
            print(f"  Status: {status}", end="")
            
            if status == 200:
                print(" ✅ SUCCESS")
                # Try to get some data for successful requests
                if method == "GET" and response.headers.get('content-type', '').startswith('application/json'):
                    try:
                        data = response.json()
                        if 'username' in data:
                            print(f"  User: {data.get('username')}")
                        elif 'full_name' in data:
                            print(f"  Repository: {data.get('full_name')}")
                        elif 'size' in data:
                            print(f"  Issues found: {data.get('size', 0)}")
                    except:
                        pass
            elif status == 403:
                print(" ❌ PERMISSION DENIED")
                print(f"  → Missing permission: {required_perm}")
            elif status == 404:
                print(" ❌ NOT FOUND")
            elif status == 401:
                print(" ❌ AUTHENTICATION FAILED")
                print("  → Check your app password")
            else:
                print(f" ⚠️  UNEXPECTED STATUS")
                
        except Exception as e:
            print(f"\n{name}: ❌ ERROR - {str(e)}")
    
    print("\n" + "=" * 70)
    print("\nPermission Summary:")
    print("✅ = Permission granted and working")
    print("❌ = Permission denied - update app password")
    print("⚠️  = Unexpected issue - investigate further")
    
    print("\nRequired App Password Permissions:")
    print("- Account: Read")
    print("- Repositories: Read") 
    print("- Issues: Read")
    print("- Pull requests: Read (recommended)")

if __name__ == "__main__":
    test_detailed_permissions()
